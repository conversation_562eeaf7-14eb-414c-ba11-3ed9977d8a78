@if (get_payment_setting('status', PAYGATE_PAYMENT_METHOD_NAME) == 1)
    @php
        $gateways = PAYGATE_SUPPORTED_GATEWAYS;
        $enabledGateways = [];
        
        foreach ($gateways as $key => $gateway) {
            if (get_payment_setting("{$key}_enabled", PAYGATE_PAYMENT_METHOD_NAME)) {
                $enabledGateways[$key] = $gateway;
            }
        }
    @endphp

    @if (!empty($enabledGateways))
        <x-plugins-payment::payment-method
            :name="PAYGATE_PAYMENT_METHOD_NAME"
            paymentName="Crypto Payment"
            :supportedCurrencies="(new Botble\PayGate\Services\Gateways\PayGatePaymentService)->supportedCurrencyCodes()"
        >
            <div class="paygate-gateway-selection mb-3">
                <label class="form-label">{{ __('Select Cryptocurrency') }}</label>
                <div class="row">
                    @foreach ($enabledGateways as $key => $gateway)
                        <div class="col-md-4 col-sm-6 mb-2">
                            <div class="form-check">
                                <input 
                                    class="form-check-input" 
                                    type="radio" 
                                    name="paygate_gateway" 
                                    id="paygate_{{ $key }}" 
                                    value="{{ $key }}"
                                    @if ($loop->first) checked @endif
                                >
                                <label class="form-check-label d-flex align-items-center" for="paygate_{{ $key }}">
                                    <img 
                                        src="{{ url("vendor/core/plugins/paygate/images/{$key}.png") }}" 
                                        alt="{{ $gateway['name'] }}" 
                                        class="me-2" 
                                        style="width: 24px; height: 24px;"
                                        onerror="this.style.display='none'"
                                    >
                                    <span>
                                        <strong>{{ $gateway['name'] }}</strong>
                                        <small class="text-muted d-block">{{ $gateway['symbol'] }} - {{ ucfirst(str_replace('-', ' ', $gateway['network'])) }}</small>
                                    </span>
                                </label>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>

            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                {{ __('You will be redirected to complete your cryptocurrency payment. Payments are processed instantly with automatic confirmation.') }}
            </div>
        </x-plugins-payment::payment-method>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const paymentForm = document.querySelector('#checkout-form, .payment-checkout-form');
                const paygateRadios = document.querySelectorAll('input[name="paygate_gateway"]');
                
                if (paymentForm && paygateRadios.length > 0) {
                    paygateRadios.forEach(radio => {
                        radio.addEventListener('change', function() {
                            // Update any UI elements based on selected gateway
                            const selectedGateway = this.value;
                            console.log('Selected PayGate gateway:', selectedGateway);
                        });
                    });
                }
            });
        </script>

        <style>
            .paygate-gateway-selection .form-check {
                border: 1px solid #dee2e6;
                border-radius: 0.375rem;
                padding: 0.75rem;
                transition: all 0.2s ease;
            }
            
            .paygate-gateway-selection .form-check:hover {
                border-color: #0d6efd;
                background-color: #f8f9fa;
            }
            
            .paygate-gateway-selection .form-check-input:checked + .form-check-label {
                color: #0d6efd;
                font-weight: 600;
            }
            
            .paygate-gateway-selection .form-check:has(.form-check-input:checked) {
                border-color: #0d6efd;
                background-color: #e7f3ff;
            }
        </style>
    @else
        <div class="alert alert-warning">
            {{ __('No cryptocurrency payment methods are currently enabled. Please contact the administrator.') }}
        </div>
    @endif
@endif
