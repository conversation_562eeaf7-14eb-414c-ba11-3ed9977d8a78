<?php

namespace Bo<PERSON>ble\PayGate\Providers;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Payment\Enums\PaymentMethodEnum;
use Bo<PERSON>ble\Payment\Enums\PaymentStatusEnum;
use Bo<PERSON>ble\Payment\Facades\PaymentMethods;
use Bo<PERSON>ble\PayGate\Forms\PayGatePaymentMethodForm;
use Botble\PayGate\Services\Gateways\PayGatePaymentService;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\ServiceProvider;

class HookServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        add_filter(PAYMENT_FILTER_ADDITIONAL_PAYMENT_METHODS, [$this, 'registerPayGateMethod'], 11, 2);
        add_filter(PAYMENT_FILTER_AFTER_POST_CHECKOUT, [$this, 'checkoutWithPayGate'], 11, 2);
        add_filter(PAYMENT_METHODS_SETTINGS_PAGE, [$this, 'addPaymentSettings'], 93);

        add_filter(BASE_FILTER_ENUM_ARRAY, function ($values, $class) {
            if ($class == PaymentMethodEnum::class) {
                $values['PAYGATE'] = PAYGATE_PAYMENT_METHOD_NAME;
            }

            return $values;
        }, 20, 2);

        add_filter(BASE_FILTER_ENUM_LABEL, function ($value, $class) {
            if ($class == PaymentMethodEnum::class && $value == PAYGATE_PAYMENT_METHOD_NAME) {
                $value = 'PayGate Crypto';
            }

            return $value;
        }, 20, 2);

        add_filter(BASE_FILTER_ENUM_HTML, function ($value, $class) {
            if ($class == PaymentMethodEnum::class && $value == PAYGATE_PAYMENT_METHOD_NAME) {
                $value = BaseHelper::renderIcon('ti ti-currency-bitcoin') . ' PayGate Crypto';
            }

            return $value;
        }, 20, 2);
    }

    public function addPaymentSettings(?string $settings): string
    {
        return $settings . PayGatePaymentMethodForm::create()->renderForm();
    }

    public function registerPayGateMethod(?string $html, array $data): ?string
    {
        PaymentMethods::method(PAYGATE_PAYMENT_METHOD_NAME, [
            'html' => view('plugins/paygate::methods', $data)->render(),
        ]);

        return $html;
    }

    public function checkoutWithPayGate(array $data, Request $request)
    {
        if ($data['type'] !== PAYGATE_PAYMENT_METHOD_NAME) {
            return $data;
        }

        $paymentData = apply_filters(PAYMENT_FILTER_PAYMENT_DATA, [], $request);

        $gateway = $request->input('paygate_gateway', 'btc');
        
        // Validate gateway is enabled
        if (!get_payment_setting("{$gateway}_enabled", PAYGATE_PAYMENT_METHOD_NAME)) {
            throw new Exception("Selected payment gateway is not enabled: {$gateway}");
        }

        $paymentService = new PayGatePaymentService();
        $paymentService
            ->setCurrency($data['currency'])
            ->setAmount($data['amount'])
            ->setPaymentData($paymentData)
            ->setGateway($gateway);

        try {
            $result = $paymentService->makePayment($request);
            
            if ($result) {
                // Store payment data in session for checkout page
                session(['paygate_payment_data' => $result]);
                
                $data['charge_id'] = $result['token'];
                $data['payment_channel'] = PAYGATE_PAYMENT_METHOD_NAME;
                $data['status'] = PaymentStatusEnum::PENDING;
                
                // Redirect to payment page
                $paymentService->redirectToCheckoutPage($result);
            }
        } catch (Exception $exception) {
            BaseHelper::logError($exception);
            throw $exception;
        }

        return $data;
    }
}
