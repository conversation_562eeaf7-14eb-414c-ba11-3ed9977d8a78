<?php

use Botble\PayGate\Http\Controllers\PayGateController;
use Illuminate\Support\Facades\Route;

Route::middleware(['core'])
    ->prefix('payment/paygate')->name('payments.paygate.')
    ->group(function (): void {
        Route::post('callback/{token}/{gateway}', [PayGateController::class, 'callback'])->name('callback');
        Route::post('webhook', [PayGateController::class, 'webhook'])->name('webhook');
        Route::get('status/{token}/{gateway}', [PayGateController::class, 'checkStatus'])->name('status');
    });
