=== Crypto Payment Gateway with Instant Payouts ===
Contributors: paygatedotto
Donate link: https://paygate.to/
Tags: woocommerce,payment,crypto,gateway
Requires at least: 5.8
Tested up to: 6.7.2
Stable tag: 1.0.7
Requires PHP: 7.2
WC requires at least: 5.8
WC tested up to: 9.6.2
License: GPLv3
License URI: http://www.gnu.org/licenses/gpl-3.0.html

Cryptocurrency Payment Gateway with instant payouts to your wallet and without KYC hosted directly on your website.

== Description ==

Accept crypto payments directly on your WooCommerce website, each order is assigned unique address and a QR code shown on your own payment page. Get instant payouts to your own wallet without any KYC.

=== Features ===

* Instant Access & Instant Approval.
* No registration and No KYC.
* Instant payouts forwarded after each order payment directly to your wallet without any delays or holds.
* Unique wallet address assigned to each order for enhanced privacy and payment detection.
* QR code shown on your own site for easy customer payments.
* Automatic payment detection.
* If the customer made a mistake sending to the wrong token or sending through the wrong network our system will still forward the crypto to you automatically for all supported tokens across all chains.
* 1.5% flat rate fee + blockchain fees.
* Automatic order processing (order will be marked as paid automatically after payment).
* Track TXID and payouts from wp-admin
* You can accept payments worldwide without restrictions due to the decentralized nature of how cryptocurrencies work.
* Please check our website for the minimum transaction amount for each cryptocurrency.
* Crypto icons on checkout page and QR code.
* Multi-currency support.
* Supported networks: BTC - BCH - LTC - doge - ETH - TRC20 - ERC20 - BEP20 - Arbitrum - Polygon - AVAX-C - Optimism - Base - Solana [check the full list of supported coins and minimum order value per coin](https://paygate.to/crypto-payment-gateway-no-kyc-instant-payouts/#minimumorder).

Minimum allowed order amount varies per crypto coin you can [check the full list of supported coins and minimum order value per coin](https://paygate.to/crypto-payment-gateway-no-kyc-instant-payouts/#minimumorder).

The plugin and offered service through [PayGate.to Crypto Payment Gateway API](https://paygate.to/crypto-payment-gateway-no-kyc-instant-payouts/) is subject to the [service terms](https://paygate.to/info/terms/) and [Privacy Policy](https://paygate.to/info/privacy-policy/).

== Installation ==

* After installing and activating this plugin go to WooCommerce >>> Settings >>> Payments >>> PayGate.to Crypto Payment gateway
* Activate the desired crypto coin gateway and insert your wallet address to receive instant payouts.
* Save settings and you will be ready to accept cryptocurrencies directly on your website!

= Minimum Requirements =

* WordPress 5.8 or greater
* PHP version 7.2 or greater

== Frequently Asked Questions ==

= Do I need to sign up as a merchant to use the plugin? =

No, the plugin is available to be used to accept crypto instantly without sign up because we never hold your crypto it will be automatically forwarded to you.

= When will I receive payouts? =

You will receive payouts instantly to your wallet with every order.

= How to fix There Are No Payment Methods Available Error? =

Follow the guide to [Fix WooCommerce There Are No Payment Methods Available Error](https://paygate.to/fix-woocommerce-there-are-no-payment-methods-available-error/)


= I have a problem with one of my orders? =

Please contact PayGate.to support team to guide you.

= I'm receiving payments to my wallet but orders are still pending payment? =

Our plugin is tested to mark orders as processing automatically after payment. You can follow our [guide for fixing common payment gateway issues](https://paygate.to/woocommerce-payment-gateway-troubleshooting/).

== Screenshots ==

1. screenshot-1.png
2. screenshot-2.png
3. screenshot-3.png
4. screenshot-4.png
5. screenshot-5.png

== Changelog ==

= V1.0.7 =

* Underpaid tolerance percentage option.

= V1.0.6 =

* Added support for TRUMP coin on Solana network.

= V1.0.5 =

* Fix processing order status bug for virtual downloadable products.

= V1.0.4 =

* Check allowed minimum per coin to prevent customer from sending lower than minimum.
* Fix deprecated dynamic property PHP 8.2
* Detailed error messages for checkout blocks.

= V1.0.3 =

* Solana support

= V1.0.2 =

* PYUSD Support

= V1.0.1 =

* PayGate.to acquisition
* Bug fix

= V1.0.0 =

* Initial release


== Upgrade Notice ==

Checkout new plugin features. Always make sure to insert your payout wallet for active gateways.