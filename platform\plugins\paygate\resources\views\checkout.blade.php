<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ __('Complete Your Crypto Payment') }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .payment-container {
            max-width: 600px;
            margin: 2rem auto;
            padding: 2rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .qr-code-container {
            text-align: center;
            padding: 2rem;
            background: #f8f9fa;
            border-radius: 10px;
            margin: 1.5rem 0;
        }
        
        .qr-code-image {
            max-width: 300px;
            width: 100%;
            height: auto;
            border: 3px solid #fff;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .payment-address {
            background: #e9ecef;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            word-break: break-all;
            border: 2px dashed #6c757d;
        }
        
        .copy-button {
            transition: all 0.3s ease;
        }
        
        .copy-button:hover {
            transform: translateY(-2px);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-pending {
            background: #ffc107;
            animation: pulse 2s infinite;
        }
        
        .status-completed {
            background: #28a745;
        }
        
        .status-failed {
            background: #dc3545;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .payment-info-card {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-left: 4px solid #007bff;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        
        .countdown-timer {
            font-size: 1.2rem;
            font-weight: bold;
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="payment-container">
            <div class="text-center mb-4">
                <h1 class="h3 mb-3">
                    <i class="fas fa-coins text-warning me-2"></i>
                    {{ __('Complete Your Payment') }}
                </h1>
                <p class="text-muted">{{ __('Send the exact amount to the address below') }}</p>
            </div>

            @php
                $gateway = $data['gateway'] ?? 'btc';
                $gatewayInfo = PAYGATE_SUPPORTED_GATEWAYS[$gateway] ?? ['name' => 'Bitcoin', 'symbol' => 'BTC'];
                $paymentAddress = $data['payment_address'] ?? '';
                $cryptoAmount = $data['crypto_amount'] ?? 0;
                $qrCode = $data['qr_code'] ?? '';
            @endphp

            <div class="payment-info-card">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <img 
                            src="{{ url("vendor/core/plugins/paygate/images/{$gateway}.png") }}" 
                            alt="{{ $gatewayInfo['name'] }}" 
                            style="width: 48px; height: 48px;"
                            onerror="this.style.display='none'"
                        >
                    </div>
                    <div class="col">
                        <h5 class="mb-1">{{ $gatewayInfo['name'] }} ({{ $gatewayInfo['symbol'] }})</h5>
                        <p class="mb-0 text-muted">{{ ucfirst(str_replace('-', ' ', $gatewayInfo['network'] ?? '')) }} Network</p>
                    </div>
                    <div class="col-auto">
                        <span class="status-indicator status-pending"></span>
                        <span id="payment-status">{{ __('Pending') }}</span>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="qr-code-container">
                        <h6 class="mb-3">{{ __('Scan QR Code') }}</h6>
                        @if ($qrCode)
                            <img 
                                src="data:image/png;base64,{{ $qrCode }}" 
                                alt="{{ __('Payment QR Code') }}" 
                                class="qr-code-image"
                            >
                        @else
                            <div class="alert alert-warning">
                                {{ __('QR Code not available') }}
                            </div>
                        @endif
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-4">
                        <h6>{{ __('Payment Amount') }}</h6>
                        <div class="h4 text-primary">
                            {{ number_format($cryptoAmount, 8) }} {{ $gatewayInfo['symbol'] }}
                        </div>
                    </div>

                    <div class="mb-4">
                        <h6>{{ __('Payment Address') }}</h6>
                        <div class="payment-address mb-2" id="payment-address">
                            {{ $paymentAddress }}
                        </div>
                        <button 
                            type="button" 
                            class="btn btn-outline-primary btn-sm copy-button" 
                            onclick="copyToClipboard('payment-address')"
                        >
                            <i class="fas fa-copy me-1"></i>
                            {{ __('Copy Address') }}
                        </button>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>{{ __('Important:') }}</strong>
                        {{ __('Send the exact amount to avoid payment delays. Payments are usually confirmed within a few minutes.') }}
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <div class="mb-3">
                    <span class="countdown-timer" id="countdown">{{ __('Checking payment status...') }}</span>
                </div>
                
                <button 
                    type="button" 
                    class="btn btn-primary me-2" 
                    onclick="checkPaymentStatus()"
                >
                    <i class="fas fa-sync-alt me-1"></i>
                    {{ __('Check Status') }}
                </button>
                
                <a href="{{ url('/') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-home me-1"></i>
                    {{ __('Back to Store') }}
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent.trim();
            
            navigator.clipboard.writeText(text).then(function() {
                // Show success feedback
                const button = event.target.closest('.copy-button');
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check me-1"></i>{{ __("Copied!") }}';
                button.classList.add('btn-success');
                button.classList.remove('btn-outline-primary');
                
                setTimeout(function() {
                    button.innerHTML = originalText;
                    button.classList.remove('btn-success');
                    button.classList.add('btn-outline-primary');
                }, 2000);
            }).catch(function(err) {
                console.error('Could not copy text: ', err);
                alert('{{ __("Failed to copy. Please copy manually.") }}');
            });
        }

        function checkPaymentStatus() {
            const token = '{{ $data["token"] ?? "" }}';
            const gateway = '{{ $gateway }}';
            
            if (!token || !gateway) {
                console.error('Missing token or gateway');
                return;
            }
            
            fetch(`/payment/paygate/status/${token}/${gateway}`)
                .then(response => response.json())
                .then(data => {
                    const statusElement = document.getElementById('payment-status');
                    const statusIndicator = document.querySelector('.status-indicator');
                    
                    if (data.status === 'completed') {
                        statusElement.textContent = '{{ __("Completed") }}';
                        statusIndicator.className = 'status-indicator status-completed';
                        
                        // Redirect to success page
                        setTimeout(() => {
                            window.location.href = '/checkout/success';
                        }, 2000);
                    } else if (data.status === 'failed') {
                        statusElement.textContent = '{{ __("Failed") }}';
                        statusIndicator.className = 'status-indicator status-failed';
                    } else {
                        statusElement.textContent = '{{ __("Pending") }}';
                        statusIndicator.className = 'status-indicator status-pending';
                    }
                })
                .catch(error => {
                    console.error('Error checking payment status:', error);
                });
        }

        // Auto-check payment status every 30 seconds
        setInterval(checkPaymentStatus, 30000);
        
        // Initial status check after 5 seconds
        setTimeout(checkPaymentStatus, 5000);
    </script>
</body>
</html>
