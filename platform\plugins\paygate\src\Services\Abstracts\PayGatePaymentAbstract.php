<?php

namespace Botble\PayGate\Services\Abstracts;

use Botble\Payment\Supports\PaymentHelper;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

abstract class PayGatePaymentAbstract
{
    protected string $currency;
    protected float $amount;
    protected array $paymentData;
    protected string $gateway;

    public function __construct()
    {
        $this->currency = '';
        $this->amount = 0;
        $this->paymentData = [];
        $this->gateway = '';
    }

    public function setCurrency(string $currency): self
    {
        $this->currency = $currency;

        return $this;
    }

    public function setAmount(float $amount): self
    {
        $this->amount = $amount;

        return $this;
    }

    public function setPaymentData(array $paymentData): self
    {
        $this->paymentData = $paymentData;

        return $this;
    }

    public function setGateway(string $gateway): self
    {
        $this->gateway = $gateway;

        return $this;
    }

    protected function setErrorMessageAndLogging(Exception $exception, int $line): void
    {
        PaymentHelper::log(
            PAYGATE_PAYMENT_METHOD_NAME,
            [
                'error' => $exception->getMessage(),
                'line' => $line,
                'file' => $exception->getFile(),
                'trace' => $exception->getTraceAsString(),
            ],
            [
                'gateway' => $this->gateway,
                'amount' => $this->amount,
                'currency' => $this->currency,
            ]
        );
    }

    public function execute(Request $request)
    {
        try {
            return $this->makePayment($request);
        } catch (Exception $exception) {
            $this->setErrorMessageAndLogging($exception, 1);

            return false;
        }
    }

    abstract public function makePayment(Request $request);

    abstract public function afterMakePayment(Request $request);

    public function isValidToProcessCheckout(): bool
    {
        return apply_filters('paygate_is_valid_to_process_checkout', true);
    }

    public function getOrderNotes(): array
    {
        return apply_filters('paygate_order_notes', []);
    }

    /**
     * Get supported currency codes for crypto payments
     */
    public function supportedCurrencyCodes(): array
    {
        return [
            'USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF', 'CNY', 'SEK', 'NZD',
            'MXN', 'SGD', 'HKD', 'NOK', 'TRY', 'RUB', 'INR', 'BRL', 'ZAR', 'KRW',
            'PLN', 'CZK', 'HUF', 'RON', 'BGN', 'HRK', 'DKK', 'ISK', 'THB', 'MYR',
            'PHP', 'IDR', 'VND', 'AED', 'SAR', 'QAR', 'KWD', 'BHD', 'OMR', 'JOD',
            'LBP', 'EGP', 'MAD', 'TND', 'DZD', 'LYD', 'SDG', 'ETB', 'KES', 'UGX',
            'TZS', 'RWF', 'BIF', 'DJF', 'SOS', 'MGA', 'KMF', 'SCR', 'MUR', 'MWK',
            'ZMW', 'BWP', 'SZL', 'LSL', 'NAD', 'AOA', 'MZN', 'ZWL', 'GHS', 'NGN',
            'XOF', 'XAF', 'CDF', 'GMD', 'GNF', 'LRD', 'SLL', 'CVE', 'STD', 'SHP',
        ];
    }

    /**
     * Convert fiat amount to crypto using PayGate API
     */
    protected function convertCurrency(string $fromCurrency, string $toCrypto, float $amount): ?array
    {
        $url = "https://api.paygate.to/crypto/{$toCrypto}/convert.php?value={$amount}&from=" . strtolower($fromCurrency);

        $response = Http::timeout(30)->get($url);

        if ($response->failed()) {
            throw new Exception('Currency conversion failed: ' . $response->body());
        }

        $data = $response->json();

        if (!$data || !isset($data['value_coin'])) {
            throw new Exception('Invalid currency conversion response');
        }

        return $data;
    }

    /**
     * Get crypto minimum amount
     */
    protected function getCryptoMinimum(string $crypto): ?array
    {
        $url = "https://api.paygate.to/crypto/{$crypto}/info.php";

        $response = Http::timeout(30)->get($url);

        if ($response->failed()) {
            throw new Exception('Failed to get crypto minimum: ' . $response->body());
        }

        $data = $response->json();

        if (!$data || !isset($data['minimum'])) {
            throw new Exception('Invalid crypto minimum response');
        }

        return $data;
    }

    /**
     * Get estimated blockchain fees
     */
    protected function getBlockchainFees(string $crypto): ?array
    {
        $url = "https://api.paygate.to/crypto/{$crypto}/fees.php";

        $response = Http::timeout(30)->get($url);

        if ($response->failed()) {
            throw new Exception('Failed to get blockchain fees: ' . $response->body());
        }

        $data = $response->json();

        if (!$data || !isset($data['estimated_cost_currency']['USD'])) {
            throw new Exception('Invalid blockchain fees response');
        }

        return $data;
    }

    /**
     * Generate payment wallet address
     */
    protected function generateWallet(string $crypto, string $walletAddress, string $callbackUrl): ?array
    {
        $url = "https://api.paygate.to/crypto/{$crypto}/wallet.php?address=" . urlencode($walletAddress) . '&callback=' . urlencode($callbackUrl);

        $response = Http::timeout(30)->get($url);

        if ($response->failed()) {
            throw new Exception('Wallet generation failed: ' . $response->body());
        }

        $data = $response->json();

        if (!$data || !isset($data['address_in'])) {
            throw new Exception('Invalid wallet generation response');
        }

        return $data;
    }

    /**
     * Generate QR code for payment address
     */
    protected function generateQRCode(string $crypto, string $address): ?array
    {
        $url = "https://api.paygate.to/crypto/{$crypto}/qrcode.php?address=" . urlencode($address);

        $response = Http::timeout(30)->get($url);

        if ($response->failed()) {
            throw new Exception('QR code generation failed: ' . $response->body());
        }

        $data = $response->json();

        if (!$data || !isset($data['qr_code'])) {
            throw new Exception('Invalid QR code response');
        }

        return $data;
    }
}
