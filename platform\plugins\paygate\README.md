# PayGate Crypto Payment Gateway for Botble CMS

A comprehensive cryptocurrency payment gateway plugin for Botble CMS that supports 79+ cryptocurrencies across multiple blockchain networks with instant payouts directly to your wallet.

## Features

- **79+ Supported Cryptocurrencies** across 10 blockchain networks
- **Instant Payouts** directly to your wallet addresses
- **No KYC Required** - Start accepting payments immediately
- **QR Code Generation** for easy mobile payments
- **Real-time Payment Tracking** with automatic confirmation
- **Configurable Tolerance** for underpayments
- **Optional Blockchain Fees** can be passed to customers
- **Multi-network Support** including Bitcoin, Ethereum, BSC, Polygon, and more

## Supported Networks & Cryptocurrencies

### Bitcoin Network
- Bitcoin (BTC), Bitcoin Cash (BCH), Litecoin (LTC), Dogecoin (DOGE)

### Ethereum Network (ERC20)
- ETH and 20+ ERC20 tokens including USDC, USDT, DAI, LINK, UNI, etc.

### Binance Smart Chain (BEP20)
- BNB and 15+ BEP20 tokens including CAKE, BUSD, etc.

### Polygon Network
- MATIC and 10+ Polygon tokens

### Arbitrum Network
- ARB and 9+ Arbitrum tokens

### Optimism Network
- OP and 8+ Optimism tokens

### Avalanche C-Chain
- AVAX and 9+ Avalanche tokens

### Base Network
- ETH and 4+ Base tokens

### Tron Network (TRC20)
- TRX and 5+ TRC20 tokens

### Solana Network
- SOL and 7+ Solana tokens

## Installation

1. Copy the `paygate` folder to `platform/plugins/`
2. Go to Admin Panel → Plugins
3. Activate the "PayGate Crypto Payment Gateway" plugin
4. Go to Admin Panel → Payments → Payment Methods
5. Configure the PayGate settings

## Configuration

### Basic Setup
1. Enable the payment method
2. Select which cryptocurrencies you want to accept
3. Enter your wallet addresses for each enabled cryptocurrency
4. Configure tolerance percentages for underpayments
5. Optionally enable blockchain fees to be paid by customers

### Wallet Addresses
- Enter your own wallet addresses where you want to receive payments
- Each cryptocurrency requires its corresponding wallet address
- Ensure addresses are correct as payments cannot be reversed

### Tolerance Settings
- Set underpayment tolerance (0-10%)
- Recommended: 1-5% to account for network fees and exchange rate fluctuations

### Blockchain Fees
- Enable to add estimated blockchain fees to order total
- Fees are calculated in real-time and added to the payment amount

## Payment Flow

1. Customer selects cryptocurrency at checkout
2. System converts order total to selected cryptocurrency
3. Temporary payment address is generated
4. Customer scans QR code or copies address to send payment
5. Payment is automatically detected and confirmed
6. Funds are instantly forwarded to your wallet address

## API Integration

This plugin uses the PayGate.to API for:
- Real-time currency conversion
- Temporary wallet generation
- QR code creation
- Payment monitoring
- Instant forwarding

## Security Features

- Secure API communication with PayGate.to
- No private keys stored on your server
- Payments go directly to your wallets
- Real-time payment verification
- Automatic timeout handling

## Requirements

- Botble CMS 7.3.0 or higher
- Payment plugin enabled
- PHP 8.0 or higher
- cURL extension enabled
- Internet connection for API calls

## Support

For support and documentation, visit:
- Plugin Documentation: [PayGate.to Documentation](https://paygate.to/docs)
- API Reference: [PayGate.to API](https://documenter.getpostman.com/view/14826208/2sAXjF8ujk)

## License

This plugin is licensed under the MIT License.

## Changelog

### Version 1.0.0
- Initial release
- Support for 79+ cryptocurrencies
- Multi-network blockchain support
- Real-time payment processing
- QR code generation
- Configurable settings per cryptocurrency
- Instant payout functionality
