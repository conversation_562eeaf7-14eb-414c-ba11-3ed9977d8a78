/**
 * PayGate Crypto Payment Gateway JavaScript
 */

(function($) {
    'use strict';

    const PayGate = {
        init: function() {
            this.bindEvents();
            this.initGatewaySelection();
        },

        bindEvents: function() {
            // Gateway selection change
            $(document).on('change', 'input[name="paygate_gateway"]', this.onGatewayChange);
            
            // Copy address button
            $(document).on('click', '.paygate-copy-btn', this.copyToClipboard);
            
            // Payment status check
            if ($('.paygate-checkout-container').length) {
                this.initPaymentStatusCheck();
            }
        },

        initGatewaySelection: function() {
            const $gateways = $('input[name="paygate_gateway"]');
            
            if ($gateways.length > 0) {
                // Highlight selected gateway
                this.highlightSelectedGateway();
                
                // Auto-select first gateway if none selected
                if (!$gateways.filter(':checked').length) {
                    $gateways.first().prop('checked', true).trigger('change');
                }
            }
        },

        onGatewayChange: function() {
            const selectedGateway = $(this).val();
            const gatewayInfo = $(this).closest('.form-check').find('.form-check-label');
            
            console.log('PayGate: Selected gateway:', selectedGateway);
            
            // Update UI based on selected gateway
            PayGate.highlightSelectedGateway();
            
            // Trigger custom event
            $(document).trigger('paygate:gateway-changed', [selectedGateway, gatewayInfo]);
        },

        highlightSelectedGateway: function() {
            const $gateways = $('input[name="paygate_gateway"]');
            
            $gateways.each(function() {
                const $check = $(this).closest('.form-check');
                
                if ($(this).is(':checked')) {
                    $check.addClass('selected');
                } else {
                    $check.removeClass('selected');
                }
            });
        },

        copyToClipboard: function(e) {
            e.preventDefault();
            
            const $btn = $(this);
            const targetId = $btn.data('target') || 'payment-address';
            const $target = $('#' + targetId);
            
            if ($target.length === 0) {
                console.error('PayGate: Copy target not found:', targetId);
                return;
            }
            
            const text = $target.text().trim();
            
            // Use modern clipboard API if available
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(text).then(function() {
                    PayGate.showCopySuccess($btn);
                }).catch(function(err) {
                    console.error('PayGate: Copy failed:', err);
                    PayGate.fallbackCopy(text, $btn);
                });
            } else {
                PayGate.fallbackCopy(text, $btn);
            }
        },

        fallbackCopy: function(text, $btn) {
            // Fallback for older browsers
            const $temp = $('<textarea>');
            $('body').append($temp);
            $temp.val(text).select();
            
            try {
                document.execCommand('copy');
                PayGate.showCopySuccess($btn);
            } catch (err) {
                console.error('PayGate: Fallback copy failed:', err);
                alert('Failed to copy. Please copy manually: ' + text);
            }
            
            $temp.remove();
        },

        showCopySuccess: function($btn) {
            const originalHtml = $btn.html();
            const originalClass = $btn.attr('class');
            
            $btn.html('<i class="fas fa-check me-1"></i>Copied!')
                .removeClass('btn-outline-primary')
                .addClass('btn-success');
            
            setTimeout(function() {
                $btn.html(originalHtml).attr('class', originalClass);
            }, 2000);
        },

        initPaymentStatusCheck: function() {
            const token = window.paygateToken;
            const gateway = window.paygateGateway;
            
            if (!token || !gateway) {
                console.error('PayGate: Missing token or gateway for status check');
                return;
            }
            
            // Check status immediately
            setTimeout(() => this.checkPaymentStatus(token, gateway), 5000);
            
            // Set up periodic status checks
            this.statusCheckInterval = setInterval(() => {
                this.checkPaymentStatus(token, gateway);
            }, 30000);
        },

        checkPaymentStatus: function(token, gateway) {
            const $statusElement = $('#payment-status');
            const $statusIndicator = $('.paygate-status-indicator');
            
            $.ajax({
                url: `/payment/paygate/status/${token}/${gateway}`,
                method: 'GET',
                timeout: 10000,
                success: function(response) {
                    if (response.status === 'completed') {
                        $statusElement.text('Completed');
                        $statusIndicator.removeClass('paygate-status-pending paygate-status-failed')
                                      .addClass('paygate-status-completed');
                        
                        // Clear interval and redirect
                        clearInterval(PayGate.statusCheckInterval);
                        
                        setTimeout(() => {
                            window.location.href = '/checkout/success';
                        }, 2000);
                        
                    } else if (response.status === 'failed') {
                        $statusElement.text('Failed');
                        $statusIndicator.removeClass('paygate-status-pending paygate-status-completed')
                                      .addClass('paygate-status-failed');
                        
                        clearInterval(PayGate.statusCheckInterval);
                        
                    } else {
                        $statusElement.text('Pending');
                        $statusIndicator.removeClass('paygate-status-completed paygate-status-failed')
                                      .addClass('paygate-status-pending');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('PayGate: Status check failed:', error);
                }
            });
        },

        // Utility functions
        formatCryptoAmount: function(amount, decimals = 8) {
            return parseFloat(amount).toFixed(decimals).replace(/\.?0+$/, '');
        },

        showNotification: function(message, type = 'info') {
            // Simple notification system
            const $notification = $(`
                <div class="alert alert-${type} alert-dismissible fade show paygate-notification" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `);
            
            $('body').prepend($notification);
            
            setTimeout(() => {
                $notification.fadeOut(() => $notification.remove());
            }, 5000);
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        PayGate.init();
    });

    // Expose PayGate object globally
    window.PayGate = PayGate;

})(jQuery);
