<?php

namespace Botble\PayGate\Services\Gateways;

use Botble\PayGate\Services\Abstracts\PayGatePaymentAbstract;
use Botble\Payment\Enums\PaymentStatusEnum;
use Botble\Payment\Supports\PaymentHelper;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class PayGatePaymentService extends PayGatePaymentAbstract
{
    public function makePayment(Request $request)
    {
        $gateway = $request->input('gateway', 'btc');
        $this->setGateway($gateway);
        
        // Get gateway configuration
        $walletAddress = get_payment_setting("{$gateway}_wallet_address", PAYGATE_PAYMENT_METHOD_NAME);
        $tolerancePercentage = get_payment_setting("{$gateway}_tolerance_percentage", PAYGATE_PAYMENT_METHOD_NAME, '1');
        $blockchainFees = get_payment_setting("{$gateway}_blockchain_fees", PAYGATE_PAYMENT_METHOD_NAME, 'no');
        
        if (empty($walletAddress)) {
            throw new Exception("Wallet address not configured for {$gateway}");
        }
        
        // Convert currency to crypto
        $conversion = $this->convertCurrency($this->currency, $gateway, $this->amount);
        $cryptoAmount = (float) $conversion['value_coin'];
        
        // Add blockchain fees if enabled
        if ($blockchainFees === 'yes') {
            $fees = $this->getBlockchainFees($gateway);
            $feeInUsd = (float) $fees['estimated_cost_currency']['USD'];
            
            // Convert fee back to crypto
            $feeConversion = $this->convertCurrency('usd', $gateway, $feeInUsd);
            $cryptoAmount += (float) $feeConversion['value_coin'];
        }
        
        // Check minimum amount
        $minimumInfo = $this->getCryptoMinimum($gateway);
        $minimum = (float) $minimumInfo['minimum'];
        
        if ($cryptoAmount < $minimum) {
            throw new Exception("Payment amount is below minimum required for {$gateway}");
        }
        
        // Generate callback URL
        $orderId = Arr::get($this->paymentData, 'order_id');
        $token = Str::random(32);
        $callbackUrl = route('payments.paygate.callback', ['token' => $token, 'gateway' => $gateway]);
        
        // Generate payment wallet
        $wallet = $this->generateWallet($gateway, $walletAddress, $callbackUrl);
        $paymentAddress = $wallet['address_in'];
        $ipnToken = $wallet['ipn_token'];
        
        // Generate QR code
        $qrCode = $this->generateQRCode($gateway, $paymentAddress);
        $qrCodeImage = $qrCode['qr_code'];
        
        // Store payment data
        $paymentData = [
            'gateway' => $gateway,
            'payment_address' => $paymentAddress,
            'crypto_amount' => $cryptoAmount,
            'tolerance_percentage' => $tolerancePercentage,
            'ipn_token' => $ipnToken,
            'qr_code' => $qrCodeImage,
            'token' => $token,
            'wallet_address' => $walletAddress,
            'callback_url' => $callbackUrl,
        ];
        
        // Log payment creation
        PaymentHelper::log(
            PAYGATE_PAYMENT_METHOD_NAME,
            ['payment_created' => $paymentData],
            ['order_id' => $orderId, 'gateway' => $gateway]
        );
        
        return $paymentData;
    }

    public function afterMakePayment(Request $request)
    {
        // Handle post-payment processing if needed
    }

    public function redirectToCheckoutPage(array $data): void
    {
        echo view('plugins/paygate::checkout', [
            'data' => $data,
        ]);

        exit();
    }
}
