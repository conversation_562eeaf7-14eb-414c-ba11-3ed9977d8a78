<div class="alert alert-info">
    <h6><i class="fas fa-info-circle me-2"></i>{{ __('PayGate Crypto Payment Gateway Setup Instructions') }}</h6>
    
    <div class="mt-3">
        <h6>{{ __('Configuration Steps:') }}</h6>
        <ol>
            <li>{{ __('Enable the payment gateways you want to accept by checking the corresponding checkboxes below') }}</li>
            <li>{{ __('For each enabled gateway, enter your wallet address where you want to receive payments') }}</li>
            <li>{{ __('Configure the underpaid tolerance percentage (recommended: 1-5%)') }}</li>
            <li>{{ __('Optionally enable blockchain fees to be paid by customers') }}</li>
            <li>{{ __('Save the settings and test with a small transaction') }}</li>
        </ol>
    </div>

    <div class="mt-3">
        <h6>{{ __('Supported Networks:') }}</h6>
        <ul class="list-unstyled">
            <li><strong>{{ __('Bitcoin Network:') }}</strong> BTC, BCH, LTC, DOGE</li>
            <li><strong>{{ __('Ethereum Network:') }}</strong> ETH and 20+ ERC20 tokens</li>
            <li><strong>{{ __('Binance Smart Chain:') }}</strong> BNB and 15+ BEP20 tokens</li>
            <li><strong>{{ __('Polygon Network:') }}</strong> MATIC and 10+ tokens</li>
            <li><strong>{{ __('Arbitrum Network:') }}</strong> ARB and 9+ tokens</li>
            <li><strong>{{ __('Optimism Network:') }}</strong> OP and 8+ tokens</li>
            <li><strong>{{ __('Avalanche C-Chain:') }}</strong> AVAX and 9+ tokens</li>
            <li><strong>{{ __('Base Network:') }}</strong> ETH and 4+ tokens</li>
            <li><strong>{{ __('Tron Network:') }}</strong> TRX and 5+ TRC20 tokens</li>
            <li><strong>{{ __('Solana Network:') }}</strong> SOL and 7+ tokens</li>
        </ul>
    </div>

    <div class="mt-3">
        <h6>{{ __('Features:') }}</h6>
        <ul>
            <li>{{ __('Instant payouts directly to your wallet') }}</li>
            <li>{{ __('No KYC or registration required') }}</li>
            <li>{{ __('Automatic payment confirmation') }}</li>
            <li>{{ __('QR code generation for easy payments') }}</li>
            <li>{{ __('Configurable underpayment tolerance') }}</li>
            <li>{{ __('Optional blockchain fee coverage') }}</li>
            <li>{{ __('Real-time payment status updates') }}</li>
        </ul>
    </div>

    <div class="mt-3">
        <h6>{{ __('Security Notes:') }}</h6>
        <ul>
            <li>{{ __('Always use your own wallet addresses') }}</li>
            <li>{{ __('Test with small amounts first') }}</li>
            <li>{{ __('Keep your wallet addresses secure') }}</li>
            <li>{{ __('Monitor payments regularly') }}</li>
        </ul>
    </div>

    <div class="mt-3">
        <p class="mb-0">
            <strong>{{ __('API Provider:') }}</strong> 
            <a href="https://paygate.to" target="_blank" rel="noopener">PayGate.to</a> - 
            {{ __('Professional cryptocurrency payment processing service') }}
        </p>
    </div>
</div>
