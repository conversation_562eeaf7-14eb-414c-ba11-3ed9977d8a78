<?php

namespace Bo<PERSON>ble\PayGate\Http\Controllers;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Payment\Enums\PaymentStatusEnum;
use Botble\Payment\Supports\PaymentHelper;
use Exception;
use Illuminate\Http\Request;

class PayGateController extends BaseController
{
    public function callback(
        string $token,
        string $gateway,
        Request $request,
        BaseHttpResponse $response
    ): BaseHttpResponse {
        // Log the callback request for debugging
        PaymentHelper::log(
            PAYGATE_PAYMENT_METHOD_NAME,
            ['callback_request' => $request->all()],
            ['token' => $token, 'gateway' => $gateway, 'headers' => $request->headers->all()]
        );

        try {
            // Get payment data from the callback
            $paymentId = $request->input('id');
            $amount = $request->input('amount');
            $currency = $request->input('currency');
            $email = $request->input('email');
            $status = $request->input('status');
            $valueCoin = $request->input('value_coin');

            if (!$paymentId) {
                PaymentHelper::log(
                    PAYGATE_PAYMENT_METHOD_NAME,
                    ['error' => 'Missing payment ID'],
                    ['token' => $token, 'gateway' => $gateway]
                );

                return $response
                    ->setNextUrl(PaymentHelper::getCancelURL($token))
                    ->withInput()
                    ->setMessage(__('Payment failed: Missing payment ID'));
            }

            // Determine payment status
            $paymentStatus = PaymentStatusEnum::PENDING;
            if ($status === 'completed' || $status === 'confirmed') {
                $paymentStatus = PaymentStatusEnum::COMPLETED;
            } elseif ($status === 'failed' || $status === 'cancelled') {
                $paymentStatus = PaymentStatusEnum::FAILED;
            }

            // Get tolerance percentage for this gateway
            $tolerancePercentage = get_payment_setting("{$gateway}_tolerance_percentage", PAYGATE_PAYMENT_METHOD_NAME, '1');
            $tolerance = (float) $tolerancePercentage;

            // Validate payment amount with tolerance
            if ($paymentStatus === PaymentStatusEnum::COMPLETED && $valueCoin) {
                $expectedAmount = session("paygate_expected_amount_{$token}");
                if ($expectedAmount) {
                    $receivedAmount = (float) $valueCoin;
                    $minimumAcceptable = $expectedAmount * $tolerance;
                    
                    if ($receivedAmount < $minimumAcceptable) {
                        $paymentStatus = PaymentStatusEnum::FAILED;
                        PaymentHelper::log(
                            PAYGATE_PAYMENT_METHOD_NAME,
                            ['underpayment' => [
                                'expected' => $expectedAmount,
                                'received' => $receivedAmount,
                                'minimum_acceptable' => $minimumAcceptable,
                                'tolerance' => $tolerance,
                            ]],
                            ['token' => $token, 'gateway' => $gateway]
                        );
                    }
                }
            }

            // Log payment processing
            PaymentHelper::log(
                PAYGATE_PAYMENT_METHOD_NAME,
                [
                    'processing_payment' => [
                        'payment_id' => $paymentId,
                        'amount' => $amount,
                        'currency' => $currency,
                        'status' => $paymentStatus,
                        'gateway' => $gateway,
                    ],
                ],
                ['token' => $token]
            );

            // Process the payment
            do_action(PAYMENT_ACTION_PAYMENT_PROCESSED, [
                'amount' => $amount,
                'currency' => $currency,
                'charge_id' => $paymentId,
                'payment_channel' => PAYGATE_PAYMENT_METHOD_NAME,
                'status' => $paymentStatus,
                'order_id' => $request->input('order_id'),
                'customer_id' => $request->input('customer_id'),
                'customer_type' => $request->input('customer_type'),
                'gateway' => $gateway,
            ]);

            if ($paymentStatus === PaymentStatusEnum::COMPLETED) {
                PaymentHelper::log(
                    PAYGATE_PAYMENT_METHOD_NAME,
                    ['success' => 'Payment completed successfully'],
                    ['charge_id' => $paymentId, 'token' => $token, 'gateway' => $gateway]
                );

                return $response
                    ->setNextUrl(PaymentHelper::getRedirectURL($token) . '?charge_id=' . $paymentId)
                    ->setMessage(__('Payment completed successfully!'));
            } else {
                return $response
                    ->setNextUrl(PaymentHelper::getCancelURL($token))
                    ->withInput()
                    ->setMessage(__('Payment failed or cancelled'));
            }

        } catch (Exception $exception) {
            PaymentHelper::log(
                PAYGATE_PAYMENT_METHOD_NAME,
                ['error' => 'Callback processing failed'],
                [
                    'exception' => $exception->getMessage(),
                    'token' => $token,
                    'gateway' => $gateway,
                ]
            );

            BaseHelper::logError($exception);

            return $response
                ->setNextUrl(PaymentHelper::getCancelURL($token) . '&error_message=' . $exception->getMessage())
                ->withInput()
                ->setMessage($exception->getMessage());
        }
    }

    public function webhook(Request $request, BaseHttpResponse $response): BaseHttpResponse
    {
        // Log the webhook request for debugging
        PaymentHelper::log(
            PAYGATE_PAYMENT_METHOD_NAME,
            ['webhook_request' => $request->all()],
            ['headers' => $request->headers->all()]
        );

        try {
            // Process webhook data similar to callback
            $paymentId = $request->input('id');
            $status = $request->input('status');
            $gateway = $request->input('gateway');

            if (!$paymentId || !$gateway) {
                return $response->setMessage('Invalid webhook data')->setStatusCode(400);
            }

            // Update payment status based on webhook
            $paymentStatus = PaymentStatusEnum::PENDING;
            if ($status === 'completed' || $status === 'confirmed') {
                $paymentStatus = PaymentStatusEnum::COMPLETED;
            } elseif ($status === 'failed' || $status === 'cancelled') {
                $paymentStatus = PaymentStatusEnum::FAILED;
            }

            // Process the payment update
            do_action(PAYMENT_ACTION_PAYMENT_PROCESSED, [
                'charge_id' => $paymentId,
                'payment_channel' => PAYGATE_PAYMENT_METHOD_NAME,
                'status' => $paymentStatus,
                'gateway' => $gateway,
            ]);

            return $response->setMessage('Webhook processed successfully');

        } catch (Exception $exception) {
            BaseHelper::logError($exception);
            return $response->setMessage('Webhook processing failed')->setStatusCode(500);
        }
    }

    public function checkStatus(string $token, string $gateway, Request $request): BaseHttpResponse
    {
        try {
            // Get payment data from session
            $paymentData = session('paygate_payment_data');
            
            if (!$paymentData || $paymentData['token'] !== $token) {
                return $this->httpResponse()->setError()->setMessage('Invalid payment token');
            }

            // Here you could check payment status via API if needed
            // For now, return pending status
            return $this->httpResponse()->setData([
                'status' => 'pending',
                'message' => 'Payment is being processed...',
            ]);

        } catch (Exception $exception) {
            BaseHelper::logError($exception);
            return $this->httpResponse()->setError()->setMessage('Status check failed');
        }
    }
}
