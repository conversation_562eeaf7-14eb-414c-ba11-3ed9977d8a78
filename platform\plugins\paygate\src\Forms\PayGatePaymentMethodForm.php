<?php

namespace Bo<PERSON>ble\PayGate\Forms;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Botble\Base\Forms\FieldOptions\CheckboxFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\SelectFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\TextFieldOption;
use Botble\Base\Forms\Fields\CheckboxField;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Payment\Forms\PaymentMethodForm;

class PayGatePaymentMethodForm extends PaymentMethodForm
{
    public function setup(): void
    {
        parent::setup();

        $this
            ->paymentId(PAYGATE_PAYMENT_METHOD_NAME)
            ->paymentName('PayGate Crypto Payment Gateway')
            ->paymentDescription(__('Accept cryptocurrency payments with instant payouts to your wallet. Supports 79+ cryptocurrencies across multiple blockchains.'))
            ->paymentLogo(url('vendor/core/plugins/paygate/images/paygate.png'))
            ->paymentUrl('https://paygate.to')
            ->paymentInstructions(view('plugins/paygate::instructions')->render());

        // Add gateway configuration sections
        $this->addGatewayConfigurations();
    }

    protected function addGatewayConfigurations(): void
    {
        $gateways = PAYGATE_SUPPORTED_GATEWAYS;
        $networks = [];
        
        // Group gateways by network
        foreach ($gateways as $key => $gateway) {
            $networks[$gateway['network']][] = ['key' => $key, 'data' => $gateway];
        }

        foreach ($networks as $network => $networkGateways) {
            $this->add(
                "paygate_network_{$network}_header",
                'html',
                [
                    'html' => '<h4 class="mt-4 mb-3">' . ucfirst(str_replace('-', ' ', $network)) . ' Network</h4>',
                ]
            );

            foreach ($networkGateways as $gatewayInfo) {
                $key = $gatewayInfo['key'];
                $gateway = $gatewayInfo['data'];
                
                $this->addGatewayFields($key, $gateway);
            }
        }
    }

    protected function addGatewayFields(string $key, array $gateway): void
    {
        // Gateway enable/disable
        $this->add(
            "payment_paygate_{$key}_enabled",
            CheckboxField::class,
            CheckboxFieldOption::make()
                ->label("{$gateway['name']} ({$gateway['symbol']})")
                ->value(get_payment_setting("{$key}_enabled", PAYGATE_PAYMENT_METHOD_NAME, false))
                ->helperText("Enable {$gateway['name']} payments on {$gateway['network']} network")
        );

        // Wallet address
        $this->add(
            "payment_paygate_{$key}_wallet_address",
            TextField::class,
            TextFieldOption::make()
                ->label("{$gateway['symbol']} Wallet Address")
                ->value(BaseHelper::hasDemoModeEnabled() ? '***************************' : get_payment_setting("{$key}_wallet_address", PAYGATE_PAYMENT_METHOD_NAME))
                ->helperText("Your {$gateway['symbol']} wallet address to receive instant payouts")
                ->attributes(['data-gateway' => $key])
        );

        // Tolerance percentage
        $this->add(
            "payment_paygate_{$key}_tolerance_percentage",
            SelectField::class,
            SelectFieldOption::make()
                ->label("{$gateway['symbol']} Underpaid Tolerance")
                ->choices([
                    '1' => '0%',
                    '0.99' => '1%',
                    '0.98' => '2%',
                    '0.97' => '3%',
                    '0.96' => '4%',
                    '0.95' => '5%',
                    '0.94' => '6%',
                    '0.93' => '7%',
                    '0.92' => '8%',
                    '0.91' => '9%',
                    '0.90' => '10%',
                ])
                ->selected(get_payment_setting("{$key}_tolerance_percentage", PAYGATE_PAYMENT_METHOD_NAME, '1'))
                ->helperText("Percentage to tolerate underpayment")
        );

        // Blockchain fees
        $this->add(
            "payment_paygate_{$key}_blockchain_fees",
            CheckboxField::class,
            CheckboxFieldOption::make()
                ->label("Customer Pays {$gateway['symbol']} Blockchain Fees")
                ->value(get_payment_setting("{$key}_blockchain_fees", PAYGATE_PAYMENT_METHOD_NAME, false))
                ->helperText("Add estimated blockchain fees to the order total")
        );
    }
}
