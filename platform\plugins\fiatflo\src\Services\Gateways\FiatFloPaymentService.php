<?php

namespace Bo<PERSON>ble\FiatFlo\Services\Gateways;

use Botble\FiatFlo\Services\Abstracts\FiatFloPaymentAbstract;
use Botble\Payment\Supports\PaymentHelper;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class FiatFloPaymentService extends FiatFloPaymentAbstract
{
    protected string $baseUrl = 'https://app.fiatflo.com/api/new';

    public function makePayment(array $data): bool|string
    {
        try {
            // Validate required data
            if (empty($this->apiKey)) {
                $this->setErrorMessage('FiatFlo API key is not configured');
                return false;
            }

            // Get customer email from data - try multiple possible keys
            $customerEmail = $data['customer_email'] ??
                           $data['email'] ??
                           $data['address']['email'] ??
                           $data['billing_address']['email'] ??
                           '<EMAIL>';

            // Prepare webhook URL
            $webhookUrl = route('fiatflo.webhook');

            // Build the FiatFlo API URL according to their format:
            // GET /api/new/{api_key}/{amount}/{email}/{card_type}?webhook={webhook_url}
            $apiUrl = sprintf(
                '%s/%s/%.2f/%s/%s',
                $this->baseUrl,
                $this->apiKey,
                $this->amount,
                urlencode($customerEmail),
                $this->methodType
            );

            $requestData = [
                'api_key' => $this->apiKey,
                'amount' => $this->amount,
                'email' => $customerEmail,
                'card_type' => $this->methodType,
                'webhook' => $webhookUrl,
            ];
            do_action('payment_before_making_api_request', FIATFLO_PAYMENT_METHOD_NAME, $requestData);

            $response = Http::timeout(30)->withHeaders([
                'Accept' => 'application/json',
                'User-Agent' => 'Botble-FiatFlo-Plugin/1.0.0',
            ])->get($apiUrl, [
                'webhook' => $webhookUrl
            ]);
            $responseData = $response->json() ?? [];

            do_action('payment_after_api_response', FIATFLO_PAYMENT_METHOD_NAME, $requestData, $responseData);

            if ($response->successful()) {
                if (isset($responseData['payment_link']) && filter_var($responseData['payment_link'], FILTER_VALIDATE_URL)) {
                    $this->chargeId = $responseData['id'] ?? Str::random(20);

                    return $responseData['payment_link'];
                } else {
                    $this->setErrorMessage('Invalid payment link received from FiatFlo');
                    return false;
                }
            }

            // Handle API errors
            $errorMessage = $responseData['error'] ?? 'Failed to create payment link';
            if ($response->status() === 400) {
                $errorMessage = 'Validation error: ' . $errorMessage;
            } elseif ($response->status() === 404) {
                $errorMessage = 'Invalid API key or method type: ' . $errorMessage;
            }

            $this->setErrorMessage($errorMessage);

            return false;
        } catch (Exception $exception) {
            $this->setErrorMessageAndLogging($exception, 2);

            return false;
        }
    }

    public function afterMakePayment(array $data): void
    {
        PaymentHelper::log(
            FIATFLO_PAYMENT_METHOD_NAME,
            [
                'message' => 'Payment link created successfully',
                'charge_id' => $this->chargeId,
                'amount' => $this->amount,
                'currency' => $this->currency,
            ]
        );
    }

    public function isValidToProcessCheckout(): bool
    {
        return apply_filters('fiatflo_is_valid_to_process_checkout', true);
    }

    public function getOrderNotes(): array
    {
        return apply_filters('fiatflo_order_notes', []);
    }
}
