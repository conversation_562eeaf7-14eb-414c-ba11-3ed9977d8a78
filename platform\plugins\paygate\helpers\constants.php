<?php

if (! defined('PAYGATE_PAYMENT_METHOD_NAME')) {
    define('PAYGATE_PAYMENT_METHOD_NAME', 'paygate');
}

// Define all supported crypto gateways
if (! defined('PAYGATE_SUPPORTED_GATEWAYS')) {
    define('PAYGATE_SUPPORTED_GATEWAYS', [
        // Bitcoin and major cryptocurrencies
        'btc' => ['name' => 'Bitcoin', 'symbol' => 'BTC', 'network' => 'bitcoin'],
        'bch' => ['name' => 'Bitcoin Cash', 'symbol' => 'BCH', 'network' => 'bitcoin-cash'],
        'ltc' => ['name' => 'Litecoin', 'symbol' => 'LTC', 'network' => 'litecoin'],
        'doge' => ['name' => 'Dogecoin', 'symbol' => 'DOGE', 'network' => 'dogecoin'],
        'eth' => ['name' => 'Ethereum', 'symbol' => 'ETH', 'network' => 'ethereum'],
        'trx' => ['name' => 'Tron', 'symbol' => 'TRX', 'network' => 'tron'],
        
        // BEP20 (Binance Smart Chain) tokens
        'oneinchbep20' => ['name' => '1INCH', 'symbol' => '1INCH', 'network' => 'bep20'],
        'adabep20' => ['name' => 'Cardano', 'symbol' => 'ADA', 'network' => 'bep20'],
        'bnbbep20' => ['name' => 'BNB', 'symbol' => 'BNB', 'network' => 'bep20'],
        'btcbbep20' => ['name' => 'Bitcoin BEP20', 'symbol' => 'BTCB', 'network' => 'bep20'],
        'cakebep20' => ['name' => 'PancakeSwap', 'symbol' => 'CAKE', 'network' => 'bep20'],
        'daibep20' => ['name' => 'Dai', 'symbol' => 'DAI', 'network' => 'bep20'],
        'dogebep20' => ['name' => 'Dogecoin BEP20', 'symbol' => 'DOGE', 'network' => 'bep20'],
        'ethbep20' => ['name' => 'Ethereum BEP20', 'symbol' => 'ETH', 'network' => 'bep20'],
        'injbep20' => ['name' => 'Injective', 'symbol' => 'INJ', 'network' => 'bep20'],
        'ltcbep20' => ['name' => 'Litecoin BEP20', 'symbol' => 'LTC', 'network' => 'bep20'],
        'phptbep20' => ['name' => 'PHPToken', 'symbol' => 'PHPT', 'network' => 'bep20'],
        'shibbep20' => ['name' => 'Shiba Inu', 'symbol' => 'SHIB', 'network' => 'bep20'],
        'thcbep20' => ['name' => 'THC', 'symbol' => 'THC', 'network' => 'bep20'],
        'usdcbep20' => ['name' => 'USD Coin', 'symbol' => 'USDC', 'network' => 'bep20'],
        'usdtbep20' => ['name' => 'Tether', 'symbol' => 'USDT', 'network' => 'bep20'],
        'virtubep20' => ['name' => 'Virtua', 'symbol' => 'TVK', 'network' => 'bep20'],
        'xrpbep20' => ['name' => 'XRP BEP20', 'symbol' => 'XRP', 'network' => 'bep20'],
        
        // ERC20 (Ethereum) tokens
        'oneincherc20' => ['name' => '1INCH', 'symbol' => '1INCH', 'network' => 'erc20'],
        'arberc20' => ['name' => 'Arbitrum', 'symbol' => 'ARB', 'network' => 'erc20'],
        'bnberc20' => ['name' => 'BNB ERC20', 'symbol' => 'BNB', 'network' => 'erc20'],
        'daierc20' => ['name' => 'Dai', 'symbol' => 'DAI', 'network' => 'erc20'],
        'eurcerc20' => ['name' => 'Euro Coin', 'symbol' => 'EURC', 'network' => 'erc20'],
        'eurterc20' => ['name' => 'Euro Tether', 'symbol' => 'EURT', 'network' => 'erc20'],
        'linkerc20' => ['name' => 'Chainlink', 'symbol' => 'LINK', 'network' => 'erc20'],
        'mkrerc20' => ['name' => 'Maker', 'symbol' => 'MKR', 'network' => 'erc20'],
        'nexoerc20' => ['name' => 'Nexo', 'symbol' => 'NEXO', 'network' => 'erc20'],
        'pepeerc20' => ['name' => 'Pepe', 'symbol' => 'PEPE', 'network' => 'erc20'],
        'shiberc20' => ['name' => 'Shiba Inu', 'symbol' => 'SHIB', 'network' => 'erc20'],
        'tusderc20' => ['name' => 'TrueUSD', 'symbol' => 'TUSD', 'network' => 'erc20'],
        'usdcerc20' => ['name' => 'USD Coin', 'symbol' => 'USDC', 'network' => 'erc20'],
        'usdperc20' => ['name' => 'Pax Dollar', 'symbol' => 'USDP', 'network' => 'erc20'],
        'usdterc20' => ['name' => 'Tether', 'symbol' => 'USDT', 'network' => 'erc20'],
        'verseerc20' => ['name' => 'Verse', 'symbol' => 'VERSE', 'network' => 'erc20'],
        'pyusderc20' => ['name' => 'PayPal USD', 'symbol' => 'PYUSD', 'network' => 'erc20'],
        
        // Arbitrum tokens
        'arbarbitrum' => ['name' => 'Arbitrum', 'symbol' => 'ARB', 'network' => 'arbitrum'],
        'daiarbitrum' => ['name' => 'Dai', 'symbol' => 'DAI', 'network' => 'arbitrum'],
        'etharbitrum' => ['name' => 'Ethereum', 'symbol' => 'ETH', 'network' => 'arbitrum'],
        'linkarbitrum' => ['name' => 'Chainlink', 'symbol' => 'LINK', 'network' => 'arbitrum'],
        'pepearbitrum' => ['name' => 'Pepe', 'symbol' => 'PEPE', 'network' => 'arbitrum'],
        'usdcarbitrum' => ['name' => 'USD Coin', 'symbol' => 'USDC', 'network' => 'arbitrum'],
        'usdcearbitrum' => ['name' => 'USD Coin (Bridged)', 'symbol' => 'USDC.e', 'network' => 'arbitrum'],
        'usdtarbitrum' => ['name' => 'Tether', 'symbol' => 'USDT', 'network' => 'arbitrum'],
        'wbtcarbitrum' => ['name' => 'Wrapped Bitcoin', 'symbol' => 'WBTC', 'network' => 'arbitrum'],
        
        // Polygon tokens
        'avaxpolygon' => ['name' => 'Avalanche', 'symbol' => 'AVAX', 'network' => 'polygon'],
        'manapolygon' => ['name' => 'Decentraland', 'symbol' => 'MANA', 'network' => 'polygon'],
        'polpolygon' => ['name' => 'Polygon', 'symbol' => 'POL', 'network' => 'polygon'],
        'smtpolygon' => ['name' => 'SmartMesh', 'symbol' => 'SMT', 'network' => 'polygon'],
        'usdcpolygon' => ['name' => 'USD Coin', 'symbol' => 'USDC', 'network' => 'polygon'],
        'usdcepolygon' => ['name' => 'USD Coin (Bridged)', 'symbol' => 'USDC.e', 'network' => 'polygon'],
        'usdtpolygon' => ['name' => 'Tether', 'symbol' => 'USDT', 'network' => 'polygon'],
        'virtupolygon' => ['name' => 'Virtua', 'symbol' => 'TVK', 'network' => 'polygon'],
        'wbtcpolygon' => ['name' => 'Wrapped Bitcoin', 'symbol' => 'WBTC', 'network' => 'polygon'],
        'wethpolygon' => ['name' => 'Wrapped Ethereum', 'symbol' => 'WETH', 'network' => 'polygon'],
        
        // Avalanche C-Chain tokens
        'avaxavaxc' => ['name' => 'Avalanche', 'symbol' => 'AVAX', 'network' => 'avax-c'],
        'btcbavaxc' => ['name' => 'Bitcoin.b', 'symbol' => 'BTC.b', 'network' => 'avax-c'],
        'eurcavaxc' => ['name' => 'Euro Coin', 'symbol' => 'EURC', 'network' => 'avax-c'],
        'usdcavaxc' => ['name' => 'USD Coin', 'symbol' => 'USDC', 'network' => 'avax-c'],
        'usdceavaxc' => ['name' => 'USD Coin (Bridged)', 'symbol' => 'USDC.e', 'network' => 'avax-c'],
        'usdtavaxc' => ['name' => 'Tether', 'symbol' => 'USDT', 'network' => 'avax-c'],
        'wavaxavaxc' => ['name' => 'Wrapped AVAX', 'symbol' => 'WAVAX', 'network' => 'avax-c'],
        'wbtceavaxc' => ['name' => 'Wrapped Bitcoin', 'symbol' => 'WBTC.e', 'network' => 'avax-c'],
        'wetheavaxc' => ['name' => 'Wrapped Ethereum', 'symbol' => 'WETH.e', 'network' => 'avax-c'],
        
        // Base tokens
        'daibase' => ['name' => 'Dai', 'symbol' => 'DAI', 'network' => 'base'],
        'ethbase' => ['name' => 'Ethereum', 'symbol' => 'ETH', 'network' => 'base'],
        'eurcbase' => ['name' => 'Euro Coin', 'symbol' => 'EURC', 'network' => 'base'],
        'usdcbase' => ['name' => 'USD Coin', 'symbol' => 'USDC', 'network' => 'base'],
        
        // Optimism tokens
        'daioptimism' => ['name' => 'Dai', 'symbol' => 'DAI', 'network' => 'optimism'],
        'ethoptimism' => ['name' => 'Ethereum', 'symbol' => 'ETH', 'network' => 'optimism'],
        'linkoptimism' => ['name' => 'Chainlink', 'symbol' => 'LINK', 'network' => 'optimism'],
        'opoptimism' => ['name' => 'Optimism', 'symbol' => 'OP', 'network' => 'optimism'],
        'usdcoptimism' => ['name' => 'USD Coin', 'symbol' => 'USDC', 'network' => 'optimism'],
        'usdceoptimism' => ['name' => 'USD Coin (Bridged)', 'symbol' => 'USDC.e', 'network' => 'optimism'],
        'usdtoptimism' => ['name' => 'Tether', 'symbol' => 'USDT', 'network' => 'optimism'],
        'wbtcoptimism' => ['name' => 'Wrapped Bitcoin', 'symbol' => 'WBTC', 'network' => 'optimism'],
        
        // TRC20 (Tron) tokens
        'aedttrc20' => ['name' => 'UAE Dirham Tether', 'symbol' => 'AEDT', 'network' => 'trc20'],
        'btctrc20' => ['name' => 'Bitcoin TRC20', 'symbol' => 'BTC', 'network' => 'trc20'],
        'inrttrc20' => ['name' => 'Indian Rupee Tether', 'symbol' => 'INRT', 'network' => 'trc20'],
        'tusdtrc20' => ['name' => 'TrueUSD', 'symbol' => 'TUSD', 'network' => 'trc20'],
        'usdttrc20' => ['name' => 'Tether', 'symbol' => 'USDT', 'network' => 'trc20'],
        
        // Solana tokens
        'solsol' => ['name' => 'Solana', 'symbol' => 'SOL', 'network' => 'solana'],
        'usdcsol' => ['name' => 'USD Coin', 'symbol' => 'USDC', 'network' => 'solana'],
        'usdtsol' => ['name' => 'Tether', 'symbol' => 'USDT', 'network' => 'solana'],
        'eurcsol' => ['name' => 'Euro Coin', 'symbol' => 'EURC', 'network' => 'solana'],
        'wbtcsol' => ['name' => 'Wrapped Bitcoin', 'symbol' => 'WBTC', 'network' => 'solana'],
        'wethsol' => ['name' => 'Wrapped Ethereum', 'symbol' => 'WETH', 'network' => 'solana'],
        'trumpsol' => ['name' => 'Trump', 'symbol' => 'TRUMP', 'network' => 'solana'],
    ]);
}
