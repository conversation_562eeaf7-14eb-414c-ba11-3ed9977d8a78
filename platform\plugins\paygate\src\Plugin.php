<?php

namespace Botble\PayGate;

use Bo<PERSON>ble\PluginManagement\Abstracts\PluginOperationAbstract;
use Bo<PERSON><PERSON>\Setting\Facades\Setting;

class Plugin extends PluginOperationAbstract
{
    public static function remove(): void
    {
        // Remove all PayGate settings when plugin is removed
        $gateways = array_keys(PAYGATE_SUPPORTED_GATEWAYS);
        
        $settingsToRemove = [
            'payment_paygate_name',
            'payment_paygate_description',
            'payment_paygate_status',
        ];
        
        // Add gateway-specific settings
        foreach ($gateways as $gateway) {
            $settingsToRemove[] = "payment_paygate_{$gateway}_enabled";
            $settingsToRemove[] = "payment_paygate_{$gateway}_wallet_address";
            $settingsToRemove[] = "payment_paygate_{$gateway}_tolerance_percentage";
            $settingsToRemove[] = "payment_paygate_{$gateway}_blockchain_fees";
        }
        
        Setting::delete($settingsToRemove);
    }
}
