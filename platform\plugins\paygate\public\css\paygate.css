/* PayGate Crypto Payment Gateway Styles */

.paygate-gateway-selection {
    margin: 1rem 0;
}

.paygate-gateway-selection .form-check {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
    cursor: pointer;
}

.paygate-gateway-selection .form-check:hover {
    border-color: #0d6efd;
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.paygate-gateway-selection .form-check-input:checked + .form-check-label {
    color: #0d6efd;
    font-weight: 600;
}

.paygate-gateway-selection .form-check:has(.form-check-input:checked) {
    border-color: #0d6efd;
    background-color: #e7f3ff;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.paygate-gateway-selection .form-check-label {
    display: flex;
    align-items: center;
    width: 100%;
    cursor: pointer;
}

.paygate-gateway-selection .form-check-label img {
    width: 24px;
    height: 24px;
    margin-right: 0.5rem;
    border-radius: 50%;
}

.paygate-gateway-selection .form-check-label small {
    font-size: 0.75rem;
    color: #6c757d;
}

/* Payment checkout page styles */
.paygate-checkout-container {
    max-width: 600px;
    margin: 2rem auto;
    padding: 2rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.paygate-qr-container {
    text-align: center;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 10px;
    margin: 1.5rem 0;
}

.paygate-qr-image {
    max-width: 300px;
    width: 100%;
    height: auto;
    border: 3px solid #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.paygate-address {
    background: #e9ecef;
    padding: 1rem;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    word-break: break-all;
    border: 2px dashed #6c757d;
    font-size: 0.9rem;
}

.paygate-copy-btn {
    transition: all 0.3s ease;
}

.paygate-copy-btn:hover {
    transform: translateY(-2px);
}

.paygate-status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.paygate-status-pending {
    background: #ffc107;
    animation: paygate-pulse 2s infinite;
}

.paygate-status-completed {
    background: #28a745;
}

.paygate-status-failed {
    background: #dc3545;
}

@keyframes paygate-pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.paygate-info-card {
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border-left: 4px solid #007bff;
    padding: 1.5rem;
    border-radius: 8px;
    margin: 1rem 0;
}

.paygate-countdown {
    font-size: 1.2rem;
    font-weight: bold;
    color: #dc3545;
}

/* Admin form styles */
.paygate-admin-section {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1rem 0;
    background: #f8f9fa;
}

.paygate-admin-section h4 {
    color: #495057;
    border-bottom: 2px solid #007bff;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
}

.paygate-gateway-config {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 1rem;
    margin: 0.5rem 0;
}

.paygate-gateway-config.enabled {
    border-color: #28a745;
    background: #f8fff9;
}

.paygate-gateway-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.paygate-gateway-header img {
    width: 32px;
    height: 32px;
    margin-right: 0.75rem;
    border-radius: 50%;
}

.paygate-gateway-header h6 {
    margin: 0;
    color: #495057;
}

/* Responsive design */
@media (max-width: 768px) {
    .paygate-checkout-container {
        margin: 1rem;
        padding: 1rem;
    }
    
    .paygate-qr-container {
        padding: 1rem;
    }
    
    .paygate-gateway-selection .col-md-4 {
        margin-bottom: 0.5rem;
    }
}

/* Loading animation */
.paygate-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: paygate-spin 1s linear infinite;
}

@keyframes paygate-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
